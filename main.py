import asyncio
import logging
import os
import face_recognition
from contextlib import asynccontextmanager

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from qdrant_client import QdrantClient

from https import DataRequest

load_dotenv()
logging.basicConfig(
    filename='./logs/main.log',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S',
    format='%(asctime)s %(levelname)s %(message)s',
)


async def start_server():
    """Start the uvicorn server with proper async handling."""
    config = uvicorn.Config(
        app=app,
        host='0.0.0.0',
        port=8000,
        log_level="info"
    )
    server = uvicorn.Server(config)
    await server.serve()


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        logging.info("Connecting to Qdrant Cloud...\n")

        app.qdrant_client = QdrantClient(
            url=os.getenv("QDRANT_ENDPOINT"),
            api_key=os.getenv("QDRANT_API"),
        )

        logging.info("Connected to Qdrant Cloud!\n")
    except Exception as error:
        logging.error(f"Error connecting to Qdrant Cloud: {error}")

    try:
        logging.info("Loading and initializing the model...\n")

        image_path = './images/sample-face.jpg'
        face_recognition.embedding(
            image_path,
            expand_percentage=3,
            model_name="Facenet512",
            align=True,
            normalization="base",
            anti_spoofing=True
        )

        logging.info("Model initialized successfully!\n")
    except Exception as error:
        logging.error(f"Error loading the model: {error}")

    try:
        yield
    finally:
        client = app.qdrant_client
        if client:
            try:
                client.close()
            except Exception as e:
                logging.error(f"Error closing Qdrant client: {e}")


app = FastAPI(lifespan=lifespan)
allowed_origins = os.getenv("ALLOWED_ORIGINS", '').split(",") \
    if os.getenv("APP_ENV") == "production" \
    else os.getenv("ALLOWED_ORIGINS_DEV", '').split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)


@app.post("/check_in")
async def check_in(data=DataRequest):
    logging.info("Checking in...\n")
    return {"message": "Hello World"}

if __name__ == '__main__':
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("Server stopped.")
